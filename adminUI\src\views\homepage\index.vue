<template>
  <div class="divBox relative">
    <div class="container mt-1 container-1">
      <div class="head-title">{{ $t("homepage.welcome") }}</div>
      <el-form :model="form" label-width="auto" style="max-width: 600px">
        <el-form-item :label="$t('homepage.paymentSwitch')">
          <el-tooltip placement="top">
            <template #content
              >{{ $t("homepage.paymentSwitchTip1") }}<br/>{{ $t("homepage.paymentSwitchTip2") }}<br/>{{ $t("homepage.paymentSwitchTip3") }}
            </template>
            <i class="el-icon-info" />
          </el-tooltip>
          <el-switch v-model="enable_app_pay" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">{{
            $t("homepage.submit")
          }}</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getConfigureApi, updateConfigure<PERSON>pi } from "@/api/homepage";
import { getToken } from "@/utils/auth";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
export default {
  name: "homepage",
  data() {
    return {
      enable_app_pay: false,
      app_login_mode: 0,
      form: {
        id: 100,
        sort: 1,
        status: true,
        fields: [
          {
            name: "enable_app_pay",
            value: "1",
            title: "enable_app_pay"
          }
        ]
      }
    };
  },
  mounted() {
    this.getConfig();
  },
  methods: {
    checkPermi,
    getConfig() {
      let _this = this;
      getConfigureApi(100)
        .then(res => {
          if (res.enable_app_pay && parseInt(res.enable_app_pay) == 1) {
            _this.form["fields"][0].value = "1";
            _this.enable_app_pay = true;
          }
        })
        .catch(res => {
          this.$Message.warning(res.msg);
        });
    },
    onSubmit() {
      console.log(this.enable_app_pay);
      this.form["fields"][0]["value"] = this.enable_app_pay ? "1" : "0";
      updateConfigureApi(this.form)
        .then(res => {
          this.$message.success("更新成功");
        })
        .catch(res => {
          this.$message.warning(res.msg);
        });
    }
  }
};
</script>

<style scoped lang="scss">
.head-title {
  font-size: 24px;
  line-height: 72px;
}
.container-1 {
  background-color: #fff;
  padding: 20px;
}
</style>
