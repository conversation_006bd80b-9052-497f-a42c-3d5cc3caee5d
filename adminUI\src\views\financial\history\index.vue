<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-tabs
        v-model="searchForm.extractType"
        @tab-click="onChangeType"
        class="mb20"
      >
        <el-tab-pane
          :label="$t('financial.history.walletWithdrawal')"
          name="wallet"
        ></el-tab-pane>
        <el-tab-pane
          :label="$t('financial.history.bankWithdrawal')"
          name="bank"
        ></el-tab-pane>
      </el-tabs>
      <div class="container mt-1">
        <el-form v-model="searchForm" inline size="small">
          <el-form-item :label="$t('financial.history.applicant') + '：'">
            <el-input
              v-model="searchForm.keywords"
              size="small"
              :placeholder="$t('common.enter')"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('financial.history.applicationTime') + '：'">
            <el-date-picker
              v-model="timeList"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              size="small"
              type="daterange"
              placement="bottom-end"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              style="width: 250px;"
            />
          </el-form-item>
          <el-form-item
            :label="$t('financial.history.electronicWallet') + '：'"
            v-if="searchForm.extractType == 'wallet'"
          >
            <el-select
              v-model="searchForm.walletCode"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="item in walletList"
                :key="item.value"
                :label="$t('operations.withdrawal.' + item.label)"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            :label="$t('financial.history.bankName') + '：'"
            v-if="searchForm.extractType == 'bank'"
          >
            <el-select
              v-model="searchForm.bankName"
              clearable
              :placeholder="$t('common.all')"
            >
              <el-option
                v-for="(item, index) in bankList"
                :key="index"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item :label="$t('financial.history.status') + '：'">
            <el-select
              v-model="searchForm.status"
              :placeholder="$t('common.all')"
              clearable
            >
              <el-option
                v-for="(item, index) in statusList"
                :key="index"
                :label="$t('operations.withdrawal.' + item.label)"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <el-button size="small" type="primary" class="mr10">{{
        $t("common.query")
      }}</el-button>
      <el-button size="small" type="" class="mr10" @click="resetForm">{{
        $t("common.reset")
      }}</el-button>
    </el-card>
    <el-card class="box-card" style="margin-top: 12px;">
      <div slot="header" class="clearfix">
        <el-button
          type="primary"
          size="small"
          @click="handleUpload"
          v-hasPermi="['admin:financialCenter:request:upload']"
          >{{ $t("financial.history.exportExcel") }}</el-button
        >
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        size="small"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          type="index"
          :label="$t('common.serialNumber')"
          width="110"
        >
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.applicationId')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.uid | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.applicantName')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.realName | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.withdrawalAmount')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.extractPrice | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.serviceFee')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.serviceFee | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.actualAmount')"
          min-width="100"
          ><template slot-scope="scope">{{
            scope.row.actualAmount | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.applicationTime')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.createTime | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'wallet'"
          :label="$t('financial.history.electronicWallet')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.walletCode | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'wallet'"
          :label="$t('financial.history.walletAccount')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.walletAccount | filterEmpty
          }}</template></el-table-column
        >
        <el-table-column
          v-if="searchForm.extractType === 'bank'"
          :label="$t('financial.history.bankName')"
          min-width="80"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.bankName | filterEmpty }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="searchForm.extractType === 'bank'"
          :label="$t('financial.history.bankCardNumber')"
          min-width="80"
        >
        </el-table-column>
        <el-table-column :label="$t('financial.history.name')" min-width="80"
          ><template slot-scope="scope">{{
            scope.row.nickName | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.phoneNumber')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.phone | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.transferTime')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.transferTime | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.transferResult')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.transferResult | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column :label="$t('financial.history.remark')" min-width="80"
          ><template slot-scope="scope">{{
            scope.row.mark | filterEmpty
          }}</template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.attachment')"
          min-width="80"
          v-if="searchForm.extractType === 'wallet'"
          ><template slot-scope="scope">
            <div class="demo-image__preview">
              <el-image
                style="width: 36px; height: 36px"
                :src="scope.row.voucherImage"
                :preview-src-list="[scope.row.voucherImage]"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('financial.history.operator')"
          min-width="80"
          ><template slot-scope="scope">{{
            scope.row.operator | filterEmpty
          }}</template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="mt20"
        @size-change="e => sizeChange"
        @current-change="e => pageChange"
        :current-page="searchForm.page"
        :page-sizes="[20, 40, 60, 100]"
        :page-size="searchForm.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="searchForm.total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { applyListApi, extractBankApi } from "@/api/financial";
export default {
  name: "WithdrawalHistory",
  data() {
    return {
      loading: false,
      tableData: [],

      searchForm: {
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        extractType: "wallet",
        status: "",
        page: 1,
        limit: 20,
        total: 0
      },
      timeList: [],
      dialogFormVisible: false,
      artFrom: {
        payType: "",
        file: "",
        remark: ""
      },
      statusList: [
        { label: "unapproved", value: "-1" },
        { label: "underReview", value: "0" },
        { label: "reviewed", value: "1" },
        { label: "paid", value: "2" }
      ],
      walletList: [
        { label: "ShopeePay", value: "ShopeePay" },
        { label: "DANA", value: "DANA" },
        { label: "OVO", value: "OVO" },
        { label: "Gopay", value: "Gopay" }
      ],
      bankList: []
    };
  },
  created() {},
  mounted() {
    this.getList();
    this.getBankList();
  },
  methods: {
    // 获取银行列表
    getBankList() {
      extractBankApi()
        .then(res => {
          this.bankList = res;
        })
        .catch(() => {});
    },
    // 列表
    getList(num) {
      this.loading = true;
      this.searchForm.page = num ? num : this.searchForm.page;
      this.searchForm.dateLimit = this.timeList.length
        ? this.timeList.join(",")
        : "";
      applyListApi(this.searchForm)
        .then(res => {
          this.tableData = res.list;
          this.searchForm.total = res.total;
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    resetForm() {
      this.searchForm = {
        keywords: "",
        dateLimit: "",
        bankName: "",
        walletCode: "",
        status: "",
        extractType: this.searchForm.extractType,
        page: 1,
        limit: 20,
        total: 0
      };
      this.timeList = [];
      this.getList();
    },
    //切换页数
    pageChange(index) {
      this.searchForm.page = index;
      this.getList();
    },
    //切换显示条数
    sizeChange(index) {
      this.searchForm.limit = index;
      this.getList();
    },
    handleUpload() {},
    onChangeType(tab) {
      this.resetForm();
      this.getList();
    }
  }
};
</script>
<style scoped lang="scss">
/**/
</style>
