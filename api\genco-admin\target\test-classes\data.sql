-- H2数据库测试数据初始化脚本
-- 用于测试环境的基础数据

-- 插入测试任务数据
INSERT INTO es_order_pull_task (platform, start_time, end_time, batch_no, page_no, next_page_token, status, retry_count, create_time, update_time) VALUES
('tiktok', '2024-01-01 00:00:00', '2024-01-01 23:59:59', 'TEST_BATCH_001', 1, NULL, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tiktok', '2024-01-01 00:00:00', '2024-01-01 23:59:59', 'TEST_BATCH_001', 2, 'token_123', 1, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tiktok', '2024-01-02 00:00:00', '2024-01-02 23:59:59', 'TEST_BATCH_002', 1, NULL, 2, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('tiktok', '2024-01-03 00:00:00', '2024-01-03 23:59:59', 'TEST_BATCH_003', 1, NULL, 3, 3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('shopee', '2024-01-01 00:00:00', '2024-01-01 23:59:59', 'SHOPEE_BATCH_001', 1, NULL, 0, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 插入测试进度数据
INSERT INTO es_order_pull_progress (platform, start_time, end_time, batch_no, last_page_no, last_page_token, status, update_time) VALUES
('tiktok', '2024-01-01 00:00:00', '2024-01-01 23:59:59', 'TEST_BATCH_001', 2, 'token_123', 0, CURRENT_TIMESTAMP),
('tiktok', '2024-01-02 00:00:00', '2024-01-02 23:59:59', 'TEST_BATCH_002', 1, NULL, 1, CURRENT_TIMESTAMP),
('tiktok', '2024-01-03 00:00:00', '2024-01-03 23:59:59', 'TEST_BATCH_003', 1, NULL, 2, CURRENT_TIMESTAMP),
('shopee', '2024-01-01 00:00:00', '2024-01-01 23:59:59', 'SHOPEE_BATCH_001', 0, NULL, 0, CURRENT_TIMESTAMP); 