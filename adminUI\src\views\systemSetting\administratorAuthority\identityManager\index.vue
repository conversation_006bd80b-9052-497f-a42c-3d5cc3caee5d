<template>
  <div class="divBox">
    <el-card class="box-card">
      <el-form inline size="small" @submit.native.prevent>
        <el-form-item>
          <el-input
            v-model="listPram.roleName"
            :placeholder="$t('admin.system.role.roleName')"
            clearable
            class="selWidth"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            @click.native="handleGetRoleList"
            >{{ $t("common.query") }}</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
            $t("common.reset")
          }}</el-button>
        </el-form-item>
      </el-form>
      <el-form inline @submit.native.prevent>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            @click="handlerOpenEdit(0)"
            v-hasPermi="[
              'admin:system:role:save',
              'admin:system:menu:cache:tree'
            ]"
            >{{ $t("admin.system.role.addRole") }}</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        :data="listData.list"
        size="mini"
        :header-cell-style="{
          fontWeight: 'bold',
          background: '#f8f8f9',
          color: '#515a6e',
          height: '40px'
        }"
      >
        <el-table-column
          :label="$t('admin.system.role.roleId')"
          prop="id"
          width="120"
        ></el-table-column>
        <el-table-column
          :label="$t('admin.system.role.roleName')"
          prop="roleName"
          min-width="130"
        />
        <el-table-column :label="$t('common.status')" prop="status">
          <template
            slot-scope="scope"
            v-if="checkPermi(['admin:system:role:update:status'])"
          >
            <el-switch
              v-model="scope.row.status"
              :active-value="true"
              :inactive-value="false"
              style="width:40px;"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('admin.system.role.createTime')"
          prop="createTime"
          min-width="150"
        />
        <el-table-column
          :label="$t('admin.system.role.updateTime')"
          prop="updateTime"
          min-width="150"
        />
        <el-table-column
          :label="$t('admin.system.role.operation')"
          min-width="130"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="handlerOpenEdit(1, scope.row)"
              v-hasPermi="['admin:system:role:info']"
              >{{ $t("admin.system.role.editRole") }}</el-button
            >
            <el-button
              size="small"
              type="text"
              @click="handlerOpenDel(scope.row)"
              v-hasPermi="['admin:system:role:delete']"
              >{{ $t("admin.system.role.deleteRole") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="listPram.page"
        :page-sizes="constants.page.limit"
        :layout="constants.page.layout"
        :total="listData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <el-dialog
      :visible.sync="editDialogConfig.visible"
      :title="
        editDialogConfig.isCreate === 0
          ? $t('admin.system.role.createIdentity')
          : $t('admin.system.role.editIdentity')
      "
      destroy-on-close
      :close-on-click-modal="false"
      width="500px"
    >
      <edit
        v-if="editDialogConfig.visible"
        :is-create="editDialogConfig.isCreate"
        :edit-data="editDialogConfig.editData"
        @hideEditDialog="hideEditDialog"
        ref="editForm"
      />
    </el-dialog>
  </div>
</template>

<script>
import * as roleApi from "@/api/role.js";
import edit from "./edit";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
export default {
  // name: "index"
  components: { edit },
  data() {
    return {
      constants: this.$constants,
      listData: { list: [] },
      listPram: {
        createTime: null,
        updateTime: null,
        level: null,
        page: 1,
        limit: this.$constants.page.limit[0],
        roleName: null,
        rules: null,
        status: null
      },
      menuList: [],
      editDialogConfig: {
        visible: false,
        isCreate: 0, // 0=创建，1=编辑
        editData: {}
      }
    };
  },
  mounted() {
    this.handleGetRoleList();
  },
  methods: {
    checkPermi,
    handlerOpenDel(rowData) {
      this.$confirm(this.$t("admin.system.role.confirmDelete")).then(() => {
        roleApi.delRole(rowData).then(data => {
          this.$message.success(this.$t("admin.system.role.deleteSuccess"));
          this.handleGetRoleList();
        });
      });
    },
    handleGetRoleList() {
      roleApi.getRoleList(this.listPram).catch(() => {
        this.$message.error(this.$t("common.fetchDataFailed"));
      });
    },
    handlerOpenEdit(isCreate, editDate) {
      isCreate === 1
        ? (this.editDialogConfig.editData = editDate)
        : (this.editDialogConfig.editData = {});
      this.editDialogConfig.isCreate = isCreate;
      this.editDialogConfig.visible = true;
    },
    hideEditDialog() {
      this.editDialogConfig.visible = false;
      this.handleGetRoleList();
    },
    handleSizeChange(val) {
      this.listPram.limit = val;
      this.handleGetRoleList(this.listPram);
    },
    handleCurrentChange(val) {
      this.listPram.page = val;
      this.handleGetRoleList(this.listPram);
    },
    //修改状态
    handleStatusChange(row) {
      roleApi.updateRoleStatus(row).then(res => {
        this.$message.success("更新状态成功");
        this.handleGetRoleList();
      });
    },
    resetQuery() {
      this.listPram.roleName = "";
      this.handleGetRoleList();
    }
  }
};
</script>

<style scoped lang="scss"></style>
