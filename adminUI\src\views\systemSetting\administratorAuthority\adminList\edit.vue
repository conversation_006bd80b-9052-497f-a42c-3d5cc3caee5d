<template>
  <div>
    <el-form
      ref="pram"
      :model="pram"
      :rules="rules"
      label-width="100px"
      @submit.native.prevent
    >
      <el-form-item :label="$t('admin.system.admin.account')" prop="account">
        <el-input
          v-model="pram.account"
          :placeholder="$t('admin.system.admin.account')"
        />
      </el-form-item>
      <el-form-item :label="$t('admin.system.admin.pwd')" prop="pwd">
        <el-input
          v-model="pram.pwd"
          :placeholder="$t('admin.system.admin.pwd')"
          clearable
          @input="handlerPwdInput"
          @clear="handlerPwdInput"
        />
      </el-form-item>
      <el-form-item
        v-if="pram.pwd"
        :label="$t('admin.system.admin.repwd')"
        prop="repwd"
      >
        <el-input
          v-model="pram.repwd"
          :placeholder="$t('admin.system.admin.repwd')"
          clearable
        />
      </el-form-item>
      <el-form-item :label="$t('admin.system.admin.realName')" prop="realName">
        <el-input
          v-model="pram.realName"
          :placeholder="$t('admin.system.admin.realName')"
        />
      </el-form-item>
      <el-form-item :label="$t('admin.system.admin.roles')" prop="roles">
        <el-select
          v-model="pram.roles"
          :placeholder="$t('admin.system.admin.roles')"
          clearable
          multiple
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in roleList.list"
            :key="index"
            :label="item.roleName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('admin.system.admin.phone')" prop="phone">
        <el-input
          type="text"
          v-model="pram.phone"
          prefix="ios-contact-outline"
          :placeholder="$t('admin.system.admin.phone')"
          size="large"
        />
      </el-form-item>
      <el-form-item :label="$t('common.status')">
        <el-switch
          v-model="pram.status"
          :active-value="true"
          :inactive-value="false"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="handlerSubmit('pram')"
          v-hasPermi="['admin:system:admin:update', 'admin:system:admin:save']"
          >{{
            isCreate === 0 ? $t("common.confirm") : $t("common.update")
          }}</el-button
        >
        <el-button @click="close">{{ $t("common.cancel") }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import * as roleApi from "@/api/role.js";
import * as systemAdminApi from "@/api/systemadmin.js";
import { Debounce } from "@/utils/validate";
export default {
  // name: "edit"
  components: {},
  props: {
    isCreate: {
      type: Number,
      required: true
    },
    editData: {
      type: Object,
      default: () => {
        return { rules: [] };
      }
    }
  },
  data() {
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        return callback(
          new Error(this.$t("admin.system.admin.validatePhone.required"))
        );
      } else if (!/^1[3456789]\d{9}$/.test(value)) {
        callback(
          new Error(this.$t("admin.system.admin.validatePhone.formatError"))
        );
      } else {
        callback();
      }
    };

    const validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(
          new Error(this.$t("admin.system.admin.validatePass.required"))
        );
      } else if (value !== this.pram.pwd) {
        callback(
          new Error(this.$t("admin.system.admin.validatePass.notMatch"))
        );
      } else {
        callback();
      }
    };
    return {
      constants: this.$constants,
      pram: {
        account: null,
        level: null,
        pwd: null,
        repwd: null,
        realName: null,
        roles: [],
        status: null,
        id: null,
        phone: null
      },
      roleList: [],
      rules: {
        account: [
          {
            required: true,
            message: this.$t("admin.system.admin.validateAccount.required"),
            trigger: ["blur", "change"]
          }
        ],
        pwd: [
          {
            required: true,
            message: this.$t("admin.system.admin.validatePassword.required"),
            trigger: ["blur", "change"]
          }
        ],
        repwd: [
          {
            required: true,
            message: this.$t(
              "admin.system.admin.validateConfirmPassword.required"
            ),
            validator: validatePass,
            trigger: ["blur", "change"]
          }
        ],
        realName: [
          {
            required: true,
            message: this.$t("admin.system.admin.validateRealName.required"),
            trigger: ["blur", "change"]
          }
        ],
        roles: [
          {
            required: true,
            message: this.$t("admin.system.admin.validateRoles.required"),
            trigger: ["blur", "change"]
          }
        ],
        phone: [
          {
            required: true,
            message: this.$t("admin.system.admin.validatePhone.required"),
            trigger: ["blur", "change"]
          }
        ]
      }
    };
  },
  mounted() {
    this.initEditData();
    this.handleGetRoleList();
  },
  methods: {
    close() {
      this.$emit("hideEditDialog");
    },
    handleGetRoleList() {
      const _pram = {
        page: 1,
        limit: this.constants.page.limit[4],
        status: 1
      };
      roleApi.getRoleList(_pram).then(data => {
        this.roleList = data;
        let arr = [];
        data.list.forEach(item => {
          arr.push(item.id);
        });
        if (!arr.includes(Number.parseInt(this.pram.roles))) {
          this.$set(this.pram, "roles", []);
        }
      });
    },
    initEditData() {
      if (this.isCreate !== 1) return;
      const {
        account,
        realName,
        roles,
        level,
        status,
        id,
        phone
      } = this.editData;
      this.pram.account = account;
      this.pram.realName = realName;
      const _roles = [];
      if (roles.length > 0 && !roles.includes(",")) {
        //如果权限id集合有长度并且是只有一个，就将它Push进_roles这个数组
        _roles.push(Number.parseInt(roles));
      } else {
        //否则就将多个id集合解构以后push进roles并且转换为整型
        _roles.push(...roles.split(",").map(item => Number.parseInt(item)));
      }
      this.pram.roles = _roles;
      this.pram.status = status;
      this.pram.id = id;
      this.pram.phone = phone;
      this.rules.pwd = [];
      this.rules.repwd = [];
    },
    handlerSubmit: Debounce(function(form) {
      this.$refs[form].validate(valid => {
        if (!valid) return;
        if (this.isCreate === 0) {
          this.handlerSave();
        } else {
          this.handlerEdit();
        }
      });
    }),
    handlerSave() {
      systemAdminApi.adminAdd(this.pram).then(data => {
        this.$message.success(
          this.$t("admin.system.admin.message.createSuccess")
        );
        this.$emit("hideEditDialog");
      });
    },
    handlerEdit() {
      this.pram.roles = this.pram.roles.join(",");
      systemAdminApi.adminUpdate(this.pram).then(data => {
        this.$message.success(
          this.$t("admin.system.admin.message.updateSuccess")
        );
        this.$emit("hideEditDialog");
      });
    },
    rulesSelect(selectKeys) {
      this.pram.rules = selectKeys;
    },
    handlerPwdInput(val) {
      if (!val) {
        this.rules.pwd = [];
        this.rules.repwd = [];
        return;
      }
      this.rules.pwd = [
        {
          required: true,
          message: this.$t("admin.system.admin.validatePassword.required"),
          trigger: ["blur", "change"]
        },
        {
          min: 6,
          max: 20,
          message: this.$t("admin.system.admin.validatePassword.lengthError"),
          trigger: ["blur", "change"]
        }
      ];
      this.rules.repwd = [
        {
          required: true,
          message: this.$t(
            "admin.system.admin.validateConfirmPassword.required"
          ),
          validator: (rule, value, callback) => {
            if (value === "") {
              callback(
                new Error(this.$t("admin.system.admin.validatePass.notMatch"))
              );
            } else if (value !== this.pram.pwd) {
              callback(
                new Error(this.$t("admin.system.admin.validatePass.notMatch"))
              );
            } else {
              callback();
            }
          },
          trigger: ["blur", "change"]
        }
      ];
    }
  }
};
</script>

<style scoped></style>
