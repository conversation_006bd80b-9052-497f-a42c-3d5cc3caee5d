import Layout from '@/layout'

const ParameterRouter = {
  path: '/operations',
  component: Layout,
  redirect: '/operations/index',
  name: 'Operations',
  meta: {
    title: 'opsCenter',
    icon: ''
  },
  children: [
    {
      path: 'chainTransferRecord',
      component: () => import('@/views/operations/chainTransferRecord/index'),
      name: 'chainTransferRecord',
      meta: { title: 'chainTransferRecord', icon: '' }
    },
    {
      path: 'audit',
      component: () => import('@/views/operations/audit/index'),
      name: 'OperationsAduit',
      meta: { title: 'withdrawalReview', icon: '' }
    },
    {
      path: 'record',
      component: () => import('@/views/operations/record/index'),
      name: 'OperationsRecord',
      meta: { title: 'withdrawalRecords', icon: '' }
    },
  ]
}

export default ParameterRouter
