import Layout from '@/layout'

const brandProductRouter = {
  path: '/brand',
  component: Layout,
  redirect: '/brand/index',
  name: 'Product',
  meta: {
    title: 'brandCenter',
    icon: 'clipboard'
  },
  children: [
    {
      path: 'manage',
      component: () => import('@/views/brand/manage'),
      name: 'BrandManage',
      meta: { title: 'brandManage', icon: '' }
    },
    {
      path: 'product/list',
      component: () => import('@/views/brand/product/list'),
      name: 'BrandProductList',
      meta: { title: 'productManage', icon: '' }
    },
  ]
}

export default brandProductRouter
