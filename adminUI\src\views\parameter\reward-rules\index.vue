<template>
  <div class="divBox relative">
    <el-card class="box-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        size="mini"
        :highlight-current-row="true"
        :header-cell-style="{ fontWeight: 'bold' }"
      >
        <el-table-column
          type="index"
          :label="$t('common.serialNumber')"
          width="110"
        >
        </el-table-column>
        <el-table-column
          prop="id"
          :label="$t('parameter.rewardRules.rewardTemplateId')"
          min-width="120"
        />
        <el-table-column
          prop="name"
          :label="$t('parameter.rewardRules.rewardTemplateName')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="value1"
          :label="$t('parameter.rewardRules.directAgentLabel')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="value2"
          :label="$t('parameter.rewardRules.directPartnerLabel')"
          min-width="150"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="value3"
          :label="$t('parameter.rewardRules.indirectAgent2LevelLabel')"
          min-width="300"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="value4"
          :label="$t('parameter.rewardRules.indirectPartner2LevelLabel')"
          min-width="300"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="value5"
          :label="$t('parameter.rewardRules.indirectAgent3LevelLabel')"
          min-width="300"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="value6"
          :label="$t('parameter.rewardRules.indirectPartner3LevelLabel')"
          min-width="300"
          :show-overflow-tooltip="true"
        ></el-table-column>

        <el-table-column
          :label="$t('parameter.rewardRules.goldRewardPer10')"
          min-width="180"
          prop="value7"
          :show-overflow-tooltip="true"
        >
        </el-table-column>

        <el-table-column
          :label="$t('parameter.rewardRules.diamondRewardPer10')"
          width="180"
          prop="value8"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          :label="$t('parameter.rewardRules.operation')"
          min-width="100"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              size="small"
              type="text"
              @click="handleEdit(scope.row)"
              >{{ $t("parameter.rewardRules.edit") }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-dialog
        append-to-body
        :visible.sync="dialogFormVisible"
        :title="$t('parameter.rewardRules.editTitle')"
        width="750px"
        @close="handleCancle"
      >
        <el-form
          ref="elForm"
          inline
          :model="artFrom"
          :rules="rules"
          label-width="350px"
        >
          <el-form-item
            :label="$t('parameter.rewardRules.rewardTemplateId')"
            prop="id"
          >
            <el-input v-model="artFrom.id" size="small" disabled />
          </el-form-item>
          <el-form-item :label="$t('parameter.rewardRules.rewardTemplateName') + '：'" prop="name">
            <el-input v-model="artFrom.name" size="small" disabled />
          </el-form-item>
          <el-form-item :label="$t('parameter.rewardRules.directAgentLabel')" prop="value1">
            <el-input
              v-model="artFrom.value1"
              size="small"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item :label="$t('parameter.rewardRules.directPartnerLabel')" prop="value2">
            <el-input
              v-model="artFrom.value2"
              size="small"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.rewardRules.indirectAgent2LevelLabel')"
            prop="value3"
          >
            <el-input
              v-model="artFrom.value3"
              size="small"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.rewardRules.indirectPartner2LevelLabel')"
            prop="value4"
          >
            <el-input
              v-model="artFrom.value4"
              size="small"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.rewardRules.indirectAgent3LevelLabel')"
            prop="value5"
          >
            <el-input
              v-model="artFrom.value5"
              size="small"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.rewardRules.indirectPartner3LevelLabel')"
            prop="value6"
          >
            <el-input
              v-model="artFrom.value6"
              size="small"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.rewardRules.goldRewardPer10') + '：'"
            prop="value7"
          >
            <el-input
              v-model="artFrom.value7"
              size="small"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
          <el-form-item
            :label="$t('parameter.rewardRules.diamondRewardPer10') + '：'"
            prop="value8"
          >
            <el-input
              v-model="artFrom.value8"
              size="small"
              :placeholder="$t('common.enter')"
            />
          </el-form-item>
        </el-form>
        <div slot="footer">
          <el-button type="primary" @click="handelConfirm">{{
            $t("common.confirm")
          }}</el-button>
          <el-button @click="handleCancle">{{ $t("common.cancel") }}</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
import { configInfo, configSaveForm } from "@/api/parameter";
export default {
  name: "ParameterRewardRules",
  data() {
    return {
      loading: false,
      tableData: [],

      dialogFormVisible: false,

      artFrom: {
        id: "",
        name: "",
        value1: "",
        value2: "",
        value3: "",
        value4: "",
        value5: "",
        value6: "",
        value7: "",
        value8: ""
      },
      rules: {
        value1: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],
        value2: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],
        value3: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],
        value4: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],
        value5: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],
        value6: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],
        value7: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ],
        value8: [
          {
            required: true,
            message: this.$t("common.enter"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 列表
    getList() {
      this.tableData=[]
      this.loading = true;
      configInfo({ formId: "111" }).then(res => {
        if (res) {
          this.tableData.push({
            id: res.id,
            name: res.agent_invite_template_name,
            value1: res.agent_invite_agent_reward_1l,
            value2: res.partner_invite_agent_reward_1l,
            value3: res.agent_invite_agent_reward_2l,
            value4: res.partner_invite_agent_reward_2l,
            value5: res.agent_invite_agent_reward_3l,
            value6: res.partner_invite_agent_reward_3l,
            value7: res.agent_gold_member_reward_10,
            value8: res.agent_diamond_member_reward_10
          });
          configInfo({ formId: "112" })
            .then(ress => {
              if (ress) {
                this.tableData.push({
                  id: ress.id,
                  name: ress.partner_invite_template_name,
                  value1: ress.agent_invite_partner_reward_1l,
                  value2: ress.partner_invite_partner_reward_1l,
                  value3: ress.agent_invite_partner_reward_2l,
                  value4: ress.partner_invite_partner_reward_2l,
                  value5: ress.agent_invite_partner_reward_3l,
                  value6: ress.partner_invite_partner_reward_3l,
                  value7: ress.partner_gold_member_reward_10,
                  value8: ress.partner_diamond_member_reward_10
                });
                this.loading = false;
              }
            })
            .catch(() => {
              this.loading = false;
            });
        }
      });
    },
    handleEdit(row) {
      this.artFrom.id = row.id;
      this.artFrom.name = row.name;
      this.artFrom.value1 = row.value1;
      this.artFrom.value2 = row.value2;
      this.artFrom.value3 = row.value3;
      this.artFrom.value4 = row.value4;
      this.artFrom.value5 = row.value5;
      this.artFrom.value6 = row.value6;
      this.artFrom.value7 = row.value7;
      this.artFrom.value8 = row.value8;
      this.dialogFormVisible = true;
    },
    handleCancle() {
      (this.artFrom = {
        id: "",
        name: "",
        value1: "",
        value2: "",
        value3: "",
        value4: "",
        value5: "",
        value6: "",
        value7: "",
        value8: ""
      }),
        (this.dialogFormVisible = false);
    },
    handelConfirm() {
      this.$refs.elForm.validate(valid => {
        if (!valid) return;
        let param = {};
        if (this.artFrom.id === "111") {
           param = {
            id: this.artFrom.id,
            sort: 1,
            status: true,
            fields: [
              {
                name: "agent_invite_template_name",
                value: this.artFrom.name,
                title: "agent_invite_template_name"
              },
              {
                name: "agent_invite_agent_reward_1l",
                value: this.artFrom.value1,
                title: "agent_invite_agent_reward_1l"
              },
              {
                name: "partner_invite_agent_reward_1l",
                value: this.artFrom.value2,
                title: "partner_invite_agent_reward_1l"
              },
              {
                name: "agent_invite_agent_reward_2l",
                value: this.artFrom.value3,
                title: "agent_invite_agent_reward_2l"
              },
              {
                name: "partner_invite_agent_reward_2l",
                value: this.artFrom.value4,
                title: "partner_invite_agent_reward_2l"
              },
              {
                name: "agent_invite_agent_reward_3l",
                value: this.artFrom.value5,
                title: "agent_invite_agent_reward_3l"
              },
              {
                name: "partner_invite_agent_reward_3l",
                value: this.artFrom.value6,
                title: "partner_invite_agent_reward_3l"
              },
              {
                name: "agent_gold_member_reward_10",
                value: this.artFrom.value7,
                title: "agent_gold_member_reward_10"
              },
              {
                name: "agent_diamond_member_reward_10",
                value: this.artFrom.value8,
                title: "agent_diamond_member_reward_10"
              }
            ]
          };
        } else {
           param = {
            id: this.artFrom.id,
            sort: 1,
            status: true,
            fields: [
              {
                name: "partner_invite_template_name",
                value: this.artFrom.name,
                title: "partner_invite_template_name"
              },
              {
                name: "agent_invite_partner_reward_1l",
                value: this.artFrom.value1,
                title: "agent_invite_partner_reward_1l"
              },
              {
                name: "agent_invite_partner_reward_2l",
                value: this.artFrom.value2,
                title: "agent_invite_partner_reward_2l"
              },
              {
                name: "agent_invite_agent_reward_2l",
                value: this.artFrom.value3,
                title: "agent_invite_agent_reward_2l"
              },
              {
                name: "partner_invite_partner_reward_2l",
                value: this.artFrom.value4,
                title: "partner_invite_partner_reward_2l"
              },
              {
                name: "agent_invite_partner_reward_3l",
                value: this.artFrom.value5,
                title: "agent_invite_partner_reward_3l"
              },
              {
                name: "partner_invite_partner_reward_3l",
                value: this.artFrom.value6,
                title: "partner_invite_partner_reward_3l"
              },
              {
                name: "partner_gold_member_reward_10",
                value: this.artFrom.value7,
                title: "partner_gold_member_reward_10"
              },
              {
                name: "partner_diamond_member_reward_10",
                value: this.artFrom.value8,
                title: "partner_diamond_member_reward_10"
              }
            ]
          };
        }
        configSaveForm(param).then(res => {
          this.$message.success(this.$t("common.operationSuccess"));

          this.handleCancle();
          this.getList();
        });
      });
    }
  }
};
</script>

<style scoped lang="scss">
/**/
</style>