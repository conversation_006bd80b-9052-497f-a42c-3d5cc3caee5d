package com.genco.admin.task.product;

import cn.hutool.json.JSONUtil;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.utils.DateUtil;
import com.genco.service.service.StoreCombinationService;
import com.genco.service.service.StoreProductService;
import com.genco.service.service.SystemConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.invoke.ApiResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductRequestBody;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductResponseData;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.CreatorSelectAffiliateProductResponseDataProducts;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * 同步TikTok商品列表
 */
@Component
@Configuration //读取配置
@EnableScheduling // 2.开启定时任务
public class ProductTikTokAsyncTask {
    //日志
    private static final Logger logger = LoggerFactory.getLogger(ProductTikTokAsyncTask.class);

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreCombinationService storeCombinationService;

    @Autowired
    private SystemConfigService systemConfigService;

    //    @Scheduled(fixedDelay = 1000 * 60L) //1分钟同步一次数据
    public void init() {
        logger.info("---OrderTakeByUser task------produce Data with fixed rate task: Execution Time - {}", DateUtil.nowDateTime());
        try {

            ApiClient apiClient = new ApiClient();

            apiClient.setAppkey(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY));
            apiClient.setSecret(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET));
            apiClient.setTokens(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ACCESS_TOKEN));

            AffiliateCreatorV202501Api affiliateCreatorV202501Api = new AffiliateCreatorV202501Api(apiClient);
            ApiResponse<CreatorSelectAffiliateProductResponse> resp = null;

            CreatorSelectAffiliateProductRequestBody requestBody = new CreatorSelectAffiliateProductRequestBody();
            resp = affiliateCreatorV202501Api.affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(
                    apiClient.getTokens(), "application/json", null, 20, requestBody);

            String nextPageToken;
            List<CreatorSelectAffiliateProductResponseDataProducts> products;
            List<StoreProduct> storeProductList = new ArrayList<>();
            Integer totalCount;
            if (resp != null && resp.getStatusCode() == 200) {
                CreatorSelectAffiliateProductResponseData respData = resp.getData().getData();
                products = respData.getProducts();
                if (products != null && products.size() > 0) {
                    nextPageToken = respData.getNextPageToken();
                    totalCount = respData.getTotalCount();
                    StoreProduct storeProduct = null;
                    for (CreatorSelectAffiliateProductResponseDataProducts product : products) {
                        storeProduct = new StoreProduct();
                        storeProduct.setOutProductId(product.getId());
                        storeProduct.setChannel(SysConfigConstants.PRODUCT_CHANNEL_TIKTOK);
                        storeProduct.setImage(product.getMainImageUrl());
                        storeProduct.setBrand(product.getBrandName());
                        storeProduct.setStoreName(product.getTitle());
                        if (product.getCommission() != null && product.getCommission().getRate() != null) {
                            storeProduct.setCashBackRate(BigDecimal.valueOf(product.getCommission().getRate() * 0.001));
                        }
                        if (product.getCommission() != null && product.getCommission().getAmount() != null) {
                            storeProduct.setCashBackAmount(new BigDecimal(product.getCommission().getAmount()));
                        }
                        if (product.getPrice() != null && product.getPrice().getCeilingPrice() != null) {
                            storeProduct.setPrice(new BigDecimal(product.getPrice().getCeilingPrice()));
                            storeProduct.setMaxSalesPrice(new BigDecimal(product.getPrice().getCeilingPrice()));
                        }
                        if (product.getPrice() != null && product.getPrice().getFloorPrice() != null) {
                            storeProduct.setMinSalesPrice(new BigDecimal(product.getPrice().getFloorPrice()));
                        }
                        if (product.getShop() != null && product.getShop().getName() != null) {
                            storeProduct.setShopName(product.getShop().getName());
                        }
                        storeProduct.setStoreInfo(JSONUtil.toJsonStr(product));
                        storeProductList.add(storeProduct);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("OrderTakeByUser.task" + " | msg : " + e.getMessage());
        }
    }
}
